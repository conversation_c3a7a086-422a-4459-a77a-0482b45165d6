/**
 * Utility functions for handling payment status consistently across the application
 */

export enum PaymentStatusType {
  COMPLETED = 'COMPLETED',
  PARTIAL = 'PARTIAL',
  PENDING = 'PENDING',
  FAILED = 'FAILED',
  UNKNOWN = 'UNKNOWN'
}

export class PaymentStatusUtil {
  
  /**
   * Normalizes payment status to a standard format
   * @param status - The payment status from API or other sources
   * @returns Normalized payment status
   */
  static normalizePaymentStatus(status: string | null | undefined): PaymentStatusType {
    if (!status) {
      return PaymentStatusType.UNKNOWN;
    }

    const normalizedStatus = status.toLowerCase().trim();

    switch (normalizedStatus) {
      case 'completed':
      case 'paid':
      case 'success':
      case 'successful':
        return PaymentStatusType.COMPLETED;

      case 'pending':
      case 'processing':
      case 'in_progress':
        return PaymentStatusType.PENDING;

      case 'failed':
      case 'failure':
      case 'declined':
      case 'cancelled':
      case 'canceled':
      case 'error':
      case '1': // Razorpay numeric status for failed/pending payment
        return PaymentStatusType.FAILED;

      case '0': // Razorpay numeric status for pending payment
        return PaymentStatusType.PENDING;

      default:
        return PaymentStatusType.UNKNOWN;
    }
  }

  /**
   * Checks if payment status indicates a successful payment
   * @param status - The payment status to check
   * @returns True if payment is completed/successful
   */
  static isPaymentCompleted(status: string | null | undefined): boolean {
    return this.normalizePaymentStatus(status) === PaymentStatusType.COMPLETED;
  }

  /**
   * Checks if payment status indicates a pending payment
   * @param status - The payment status to check
   * @returns True if payment is pending
   */
  static isPaymentPending(status: string | null | undefined): boolean {
    return this.normalizePaymentStatus(status) === PaymentStatusType.PENDING;
  }

  /**
   * Checks if payment status indicates a failed payment
   * @param status - The payment status to check
   * @returns True if payment failed
   */
  static isPaymentFailed(status: string | null | undefined): boolean {
    return this.normalizePaymentStatus(status) === PaymentStatusType.FAILED;
  }

  /**
   * Checks if payment is partial based on booking details
   * @param booking - The booking object with payment details
   * @returns True if payment is partial
   */
  static isPartialPayment(booking: any): boolean {
    return booking?.isPartialPayment === true;
  }

  /**
   * Gets a user-friendly display text for payment status
   * @param status - The payment status
   * @param booking - Optional booking object to check for partial payment
   * @returns User-friendly status text
   */
  static getDisplayText(status: string | null | undefined, booking?: any): string {
    const normalizedStatus = this.normalizePaymentStatus(status);

    // Only show "Partial Paid" if payment is successful AND it's a partial payment
    if (normalizedStatus === PaymentStatusType.COMPLETED && booking && this.isPartialPayment(booking)) {
      return 'Partial Paid';
    }

    switch (normalizedStatus) {
      case PaymentStatusType.COMPLETED:
        return 'Paid';
      case PaymentStatusType.PENDING:
        return 'Pending';
      case PaymentStatusType.FAILED:
        return 'Failed';
      default:
        return 'Unknown';
    }
  }

  /**
   * Gets CSS class name for payment status styling
   * @param status - The payment status
   * @param booking - Optional booking object to check for partial payment
   * @returns CSS class name
   */
  static getStatusClass(status: string | null | undefined, booking?: any): string {
    const normalizedStatus = this.normalizePaymentStatus(status);

    // Only show "status-partial" if payment is successful AND it's a partial payment
    if (normalizedStatus === PaymentStatusType.COMPLETED && booking && this.isPartialPayment(booking)) {
      return 'status-partial';
    }

    switch (normalizedStatus) {
      case PaymentStatusType.COMPLETED:
        return 'status-paid';
      case PaymentStatusType.PENDING:
        return 'status-pending';
      case PaymentStatusType.FAILED:
        return 'status-failed';
      default:
        return 'status-unknown';
    }
  }
}
